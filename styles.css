/* Element UI Style Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
    background: #f5f7fa;
    height: 100vh;
    overflow: hidden;
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
}

/* Element UI Layout */
.el-layout {
    height: 100vh;
    background: #ffffff;
    overflow: hidden;
}

/* Element UI Header */
.el-header.app-header {
    height: 80px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px 0 90px; /* 为macOS窗口控制按钮预留左侧空间 */
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
    -webkit-app-region: drag; /* 允许拖拽窗口 */
}

/* macOS 特定样式优化 */
@media (platform: macOS) {
    .el-header.app-header {
        padding-left: 90px; /* macOS 需要更多左侧空间 */
    }
}

/* Windows/Linux 优化 */
.el-header.app-header.windows {
    padding-left: 24px; /* Windows/Linux 不需要额外左侧空间 */
}

/* 窗口控制区域指示器（开发调试用，可选） */
.el-header.app-header::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 80px;
    height: 100%;
    background: rgba(255, 255, 255, 0.05);
    pointer-events: none;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.el-header.app-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

/* header-left 样式已移到 header-actions 后面 */

.brand-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-icon {
    color: #ffffff;
    font-size: 32px;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.header-title {
    font-size: 22px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.header-subtitle {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
    margin-top: 2px;
}

.header-stats {
    display: flex;
    gap: 20px;
    margin-left: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 13px;
    background: rgba(255, 255, 255, 0.1);
    padding: 6px 12px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.stat-item i {
    color: #ffffff;
}

.stat-item strong {
    color: #ffffff;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 16px;
    z-index: 1;
    -webkit-app-region: no-drag; /* 按钮区域不允许拖拽 */
    align-items: center;
}

.batch-actions {
    display: flex;
    gap: 8px;
    padding-right: 16px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 10;
    pointer-events: auto;
}

.config-actions {
    display: flex;
    gap: 8px;
    z-index: 10;
    pointer-events: auto;
}

.el-button--danger {
    color: #ffffff;
    background-color: #f56565;
    border-color: #f56565;
}

.el-button--danger:hover {
    background: #fc8181;
    border-color: #fc8181;
    color: #ffffff;
}

.el-button--danger:disabled {
    background: #fed7d7;
    border-color: #fed7d7;
    color: #a0aec0;
    cursor: not-allowed;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 24px;
    flex: 1;
    z-index: 1;
    -webkit-app-region: no-drag; /* 内容区域不允许拖拽，避免误操作 */
}

/* Element UI Buttons */
.el-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    line-height: 1;
    height: 32px;
    white-space: nowrap;
    cursor: pointer;
    background: #ffffff;
    border: 1px solid #dcdfe6;
    color: #606266;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 0;
    font-weight: 500;
    font-size: 14px;
    border-radius: 4px;
    padding: 8px 15px;
    transition: 0.1s;
    text-decoration: none;
}

.el-button:hover {
    color: #409eff;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
}

.el-button--primary {
    color: #ffffff;
    background-color: #409eff;
    border-color: #409eff;
}

.el-button--primary:hover {
    background: #66b1ff;
    border-color: #66b1ff;
    color: #ffffff;
}

.el-button--success {
    color: #ffffff;
    background-color: #67c23a;
    border-color: #67c23a;
}

.el-button--success:hover {
    background: #85ce61;
    border-color: #85ce61;
    color: #ffffff;
}

.el-button--default:hover {
    color: #409eff;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
}

.el-button--info {
    color: #ffffff;
    background-color: #909399;
    border-color: #909399;
}

.el-button--info:hover {
    background: #a6a9ad;
    border-color: #a6a9ad;
    color: #ffffff;
}

.el-button--warning {
    color: #ffffff;
    background-color: #e6a23c;
    border-color: #e6a23c;
}

.el-button--warning:hover {
    background: #ebb563;
    border-color: #ebb563;
    color: #ffffff;
}

.el-button--round {
    border-radius: 20px;
    padding: 8px 18px;
}

.el-button--large {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 6px;
}

/* Element UI Container */
.el-container.main-container {
    height: calc(100vh - 140px);
}

/* Settings Page */
.settings-page {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #ffffff;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 32px;
    border-bottom: 1px solid #ebeef5;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    min-height: 80px;
}

.settings-title-section {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.settings-title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.settings-title i {
    color: #409eff;
    font-size: 22px;
}

.settings-subtitle {
    font-size: 13px;
    color: #909399;
    font-weight: 400;
}

.settings-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: flex-start;
}

.settings-content {
    flex: 1;
    padding: 32px;
    overflow-y: auto;
    background: #f8f9fa;
}

.settings-section {
    margin-bottom: 32px;
    border-radius: 12px;
    border: 1px solid #e4e7ed;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: box-shadow 0.3s ease;
}

.settings-section:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section .el-card__header {
    padding: 24px 28px;
    border-bottom: 1px solid #f0f2f5;
    background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
    border-radius: 12px 12px 0 0;
}

.settings-section .el-card__body {
    padding: 32px 28px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0;
}

.section-title i {
    color: #409eff;
    font-size: 20px;
}

.path-input-group {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-top: 4px;
}

.path-input-group .el-input__inner {
    flex: 1;
    height: 40px;
    font-size: 14px;
    border-radius: 6px;
    border: 1.5px solid #dcdfe6;
    transition: border-color 0.3s ease;
}

.path-input-group .el-input__inner:focus {
    border-color: #409eff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.path-input-group .el-button {
    height: 40px;
    padding: 0 16px;
    white-space: nowrap;
}

.el-form-item {
    margin-bottom: 28px;
    padding: 20px;
    background: #f8fafe;
    border-radius: 8px;
    border: 1px solid #e8f3ff;
    transition: all 0.3s ease;
}

.el-form-item:hover {
    border-color: #b3d8ff;
    background: #f0f9ff;
}

.el-form-item:last-child {
    margin-bottom: 0;
}

.el-form-item__label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 15px;
    color: #303133;
    font-weight: 600;
    margin-bottom: 12px;
}

.el-form-item__label i {
    color: #409eff;
    width: 18px;
    font-size: 16px;
}

.el-form-item__description {
    font-size: 13px;
    color: #666666;
    margin-top: 8px;
    line-height: 1.5;
    padding: 8px 12px;
    background: #f0f2f5;
    border-radius: 4px;
    border-left: 3px solid #409eff;
}

.el-form-item__checkbox {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    font-size: 15px;
    color: #303133;
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    border: 2px solid #e4e7ed;
    transition: all 0.3s ease;
    margin-top: 4px;
}

.el-form-item__checkbox:hover {
    border-color: #67c23a;
    background: #f0f9f0;
}

.el-checkbox__input {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: #67c23a;
}

.el-checkbox__label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: 500;
}

.el-checkbox__label i {
    color: #67c23a;
    font-size: 16px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 8px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafe 0%, #f0f9ff 100%);
    border-radius: 8px;
    border: 1px solid #d9ecff;
    transition: all 0.3s ease;
}

.info-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
    border-color: #b3d8ff;
}

.info-item label {
    font-weight: 600;
    color: #303133;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-item label::before {
    content: '●';
    color: #409eff;
    font-size: 12px;
}

.info-item span {
    font-family: 'SF Mono', 'Monaco', 'Menlo', monospace;
    font-size: 13px;
    color: #409eff;
    background: #ffffff;
    padding: 6px 10px;
    border-radius: 6px;
    border: 1px solid #d9ecff;
    font-weight: 500;
}

.el-aside.sidebar {
    width: 300px !important;
    background: #ffffff;
    border-right: 1px solid #ebeef5;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.sidebar-title-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.sidebar-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sidebar-title i {
    color: #409eff;
    font-size: 18px;
}

.sidebar-tools {
    display: flex;
    gap: 4px;
}

.tool-btn {
    width: 28px;
    height: 28px;
    border: 1px solid #dcdfe6;
    background: #ffffff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #909399;
}

.tool-btn:hover {
    border-color: #409eff;
    color: #409eff;
    background: #ecf5ff;
}

.search-section {
    padding: 0 0 16px 0;
}

.search-input {
    position: relative;
}

.el-input__prefix {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #c0c4cc;
    z-index: 1;
}

.search-input .el-input__inner {
    padding-left: 35px;
}

.filter-section {
    margin-bottom: 16px;
}

.filter-tabs {
    display: flex;
    background: #f4f4f5;
    border-radius: 6px;
    padding: 4px;
}

.filter-tab {
    flex: 1;
    padding: 8px 12px;
    border: none;
    background: transparent;
    color: #606266;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.filter-tab.active {
    background: #409eff;
    color: #ffffff;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

.filter-tab:hover:not(.active) {
    background: #e4e7ed;
}

.el-badge .el-badge__content {
    background-color: #409eff;
    border-radius: 10px;
    color: #ffffff;
    display: inline-block;
    font-size: 12px;
    height: 18px;
    line-height: 18px;
    padding: 0 6px;
    text-align: center;
    white-space: nowrap;
    border: 1px solid #ffffff;
}

/* Element UI Config List */
.config-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

.config-item {
    background: #ffffff;
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 8px;
    border: 1px solid #ebeef5;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.config-item:hover {
    border-color: #409eff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.config-item.active {
    border-color: #409eff;
    background: #f0f9ff;
    box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.12);
}

/* Element UI Config Item */
.config-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.config-name {
    font-weight: 500;
    color: #303133;
    font-size: 14px;
}

.config-actions {
    display: flex;
    gap: 4px;
}

.config-actions button {
    background: #f4f4f5;
    border: 1px solid #e4e7ed;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    color: #606266;
    transition: all 0.2s ease;
}

.config-actions button:hover {
    background: #ecf5ff;
    border-color: #b3d8ff;
    color: #409eff;
}

.config-info {
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
}

/* Element UI Status */
.config-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.status-running {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: #f0f9ff;
    color: #67c23a;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    border: 1px solid #e1f3d8;
}

.status-running::before {
    content: '●';
    color: #67c23a;
}

.btn-activate {
    padding: 2px 6px;
    font-size: 11px;
    background: #409eff;
    color: white;
    border: 1px solid #409eff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-activate:hover {
    background: #66b1ff;
    border-color: #66b1ff;
}

.btn-terminate {
    padding: 2px 6px;
    font-size: 11px;
    background: #f56c6c;
    color: white;
    border: 1px solid #f56c6c;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-terminate:hover {
    background: #f78989;
    border-color: #f78989;
}

/* Element UI Content Area */
.el-main.content-area {
    background: linear-gradient(135deg, #f5f7fa 0%, #ffffff 100%);
    overflow-y: auto;
    padding: 0;
}

.welcome-screen {
    padding: 40px;
}

.welcome-hero {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 40px;
    margin-bottom: 40px;
    color: white;
    position: relative;
    overflow: hidden;
}

.welcome-hero::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.hero-content {
    flex: 1;
    z-index: 1;
}

.hero-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.hero-icon i {
    font-size: 36px;
    color: #ffffff;
}

.hero-title {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 12px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin-bottom: 24px;
}

.hero-stats {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-left: 40px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 16px;
    background: rgba(255, 255, 255, 0.15);
    padding: 16px 20px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-card i {
    font-size: 24px;
    color: #ffffff;
}

.stat-content h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.stat-content p {
    font-size: 12px;
    opacity: 0.8;
    margin: 0;
}

.feature-section {
    margin-top: 40px;
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    text-align: center;
    justify-content: center;
}

.section-title i {
    color: #409eff;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
}

.feature-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #ebeef5;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.feature-card.premium {
    border: 2px solid #409eff;
    background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
}

.feature-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.feature-header i {
    font-size: 32px;
    color: #409eff;
}

.feature-badge {
    background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.feature-card h4 {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 12px;
}

.feature-card p {
    font-size: 14px;
    color: #606266;
    line-height: 1.6;
    margin-bottom: 16px;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
    font-size: 13px;
    color: #606266;
}

.feature-list i {
    color: #67c23a;
    font-size: 12px;
}

/* Element UI Form */
.config-form {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #ffffff;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 32px;
    border-bottom: 1px solid #ebeef5;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    min-height: 80px;
}

.form-title-section {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.form-title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-title i {
    color: #409eff;
    font-size: 22px;
}

.form-subtitle {
    font-size: 13px;
    color: #909399;
    font-weight: 400;
}

.form-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: flex-start;
}

/* Element UI Form Content */
.form-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f5f7fa;
}

.form-section {
    margin-bottom: 20px;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    background-color: #ffffff;
    overflow: hidden;
    color: #303133;
    transition: 0.3s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.form-section:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-card__header {
    padding: 18px 20px;
    border-bottom: 1px solid #ebeef5;
    box-sizing: border-box;
    background: #fafafa;
}

.section-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title i {
    color: #409eff;
}

.el-card__body {
    padding: 20px;
}

/* Element UI Grid System */
.el-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.el-col {
    flex: 0 0 auto;
    padding: 0 10px;
    box-sizing: border-box;
}

.el-col-8 { 
    width: 33.33333%; 
    flex: 0 0 33.33333%;
}

.el-col-12 { 
    width: 50%; 
    flex: 0 0 50%;
}

.el-col-16 { 
    width: 66.66667%; 
    flex: 0 0 66.66667%;
}

.el-col-24 { 
    width: 100%; 
    flex: 0 0 100%;
}

.el-form-item {
    margin-bottom: 18px;
}

/* Element UI Card Styles */
.el-card {
    border-radius: 8px;
    border: 1px solid #ebeef5;
    background-color: #ffffff;
    overflow: hidden;
    color: #303133;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    margin-bottom: 16px;
}

.el-card__header {
    padding: 18px 20px;
    border-bottom: 1px solid #ebeef5;
    box-sizing: border-box;
    background-color: #fafafa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.el-card__body {
    padding: 20px;
}

.el-form-item__label {
    text-align: right;
    vertical-align: middle;
    float: left;
    font-size: 14px;
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    box-sizing: border-box;
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
    text-align: left;
    float: none;
    line-height: 1.5;
}

.el-form-item__content {
    line-height: 40px;
    position: relative;
    font-size: 14px;
}

.el-input__inner {
    -webkit-appearance: none;
    background-color: #ffffff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: inherit;
    height: 40px;
    line-height: 40px;
    outline: none;
    padding: 0 15px;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    width: 100%;
}

.el-input__inner:focus {
    outline: none;
    border-color: #409eff;
}

.el-select .el-input__inner {
    cursor: pointer;
    padding-right: 35px;
}

.el-input-group {
    line-height: normal;
    display: inline-table;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.el-input-group__append {
    background-color: #f5f7fa;
    color: #909399;
    vertical-align: middle;
    display: table-cell;
    position: relative;
    border: 1px solid #dcdfe6;
    border-radius: 0 4px 4px 0;
    padding: 0 8px;
    width: 1px;
    white-space: nowrap;
    border-left: 0;
}

.el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 100%;
    left: 0;
}

/* Element UI Checkbox and Alert */
.el-checkbox {
    color: #606266;
    font-weight: 500;
    font-size: 14px;
    position: relative;
    cursor: pointer;
    display: inline-block;
    white-space: nowrap;
    user-select: none;
    margin-right: 30px;
}

.el-checkbox__input {
    white-space: nowrap;
    cursor: pointer;
    outline: none;
    display: inline-block;
    line-height: 1;
    position: relative;
    vertical-align: middle;
}

.el-checkbox__inner {
    display: inline-block;
    position: relative;
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    box-sizing: border-box;
    width: 14px;
    height: 14px;
    background-color: #ffffff;
    z-index: 1;
    transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46), background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);
}

.el-checkbox__original {
    opacity: 0;
    outline: none;
    position: absolute;
    margin: 0;
    width: 0;
    height: 0;
    z-index: -1;
}

.el-checkbox__original:checked + .el-checkbox__inner {
    background-color: #409eff;
    border-color: #409eff;
}

.el-checkbox__original:checked + .el-checkbox__inner::after {
    transform: rotate(45deg) scaleY(1);
}

.el-checkbox__inner::after {
    box-sizing: content-box;
    content: "";
    border: 1px solid #ffffff;
    border-left: 0;
    border-top: 0;
    height: 7px;
    left: 4px;
    position: absolute;
    top: 1px;
    transform: rotate(45deg) scaleY(0);
    width: 3px;
    transition: transform 0.15s ease-in 0.05s;
    transform-origin: center;
}

.el-checkbox__label {
    display: inline-block;
    padding-left: 10px;
    line-height: 19px;
    font-size: 14px;
}

.el-alert {
    width: 100%;
    padding: 8px 16px;
    margin: 0;
    box-sizing: border-box;
    border-radius: 4px;
    position: relative;
    background-color: #ffffff;
    overflow: hidden;
    opacity: 1;
    display: flex;
    align-items: center;
    transition: opacity 0.2s;
    margin-top: 8px;
}

.el-alert--info {
    background-color: #f4f4f5;
    color: #909399;
}

.el-alert__content {
    display: table-cell;
    padding: 0 8px;
}

.el-alert__title {
    font-size: 13px;
    line-height: 18px;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-top: 1px solid #ebeef5;
    font-size: 12px;
    color: #606266;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.status-left, .status-center, .status-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.status-center {
    flex: 1;
    justify-content: center;
}

.status-group {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #606266;
}

.status-group i {
    color: #909399;
}

.memory-usage {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #606266;
}

.memory-usage i {
    color: #409eff;
}

.version-info {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #909399;
    font-size: 11px;
}

.status-time {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 11px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #dc3545;
}

.status-indicator.connected::before {
    background: #28a745;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.config-item {
    animation: fadeIn 0.3s ease;
}

::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* 批量任务页面样式 */
.batch-task-page {
    padding: 20px;
    height: 100%;
    width: 100%;
    background: #f5f7fa;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.batch-task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.task-title-section {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.task-title {
    color: #303133;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.task-title i {
    color: #f39c12;
}

.task-subtitle {
    color: #909399;
    font-size: 14px;
}

.task-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
}

.batch-task-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    flex: 1;
    min-height: 0;
}

.batch-task-content .el-row {
    align-items: stretch;
}

.batch-task-content .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.batch-task-content .el-card__body {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.batch-task-content .el-form {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 运行中浏览器列表样式 */
.running-browsers-list {
    min-height: 300px;
    max-height: 500px;
    overflow-y: auto;
    border-radius: 6px;
    background: #fafafa;
    border: 1px solid #ebeef5;
    padding: 12px;
    flex: 1;
}

.running-browsers-list:empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
}

.no-browsers {
    text-align: center;
    padding: 60px 20px;
    color: #909399;
    background: #ffffff;
    border-radius: 6px;
    border: 2px dashed #e6e8eb;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 250px;
}

.no-browsers i {
    font-size: 64px;
    margin-bottom: 20px;
    color: #e6e8eb;
}

.no-browsers p {
    margin: 8px 0;
    font-size: 16px;
}

.no-browsers p:first-of-type {
    font-weight: 600;
    color: #606266;
}

.no-browsers .hint {
    font-size: 13px;
    color: #c0c4cc;
    margin-top: 8px;
}

.browser-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    margin-bottom: 12px;
    background: #fafafa;
    transition: all 0.3s ease;
}

.browser-item:hover {
    background: #f0f9ff;
    border-color: #409eff;
}

.browser-info {
    flex: 1;
}

.browser-name {
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.browser-name i {
    color: #409eff;
}

.browser-details {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #909399;
    margin-bottom: 8px;
}

.debug-info {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
}

.debug-port {
    color: #67c23a;
    font-family: monospace;
}

.debug-link {
    color: #409eff;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: color 0.3s ease;
}

.debug-link:hover {
    color: #337ecc;
}

.browser-actions {
    display: flex;
    align-items: center;
}

.browser-checkbox {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.browser-checkbox input[type="checkbox"] {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.checkmark {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: white;
    border: 2px solid #dcdfe6;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.browser-checkbox input[type="checkbox"]:checked + .checkmark {
    background: #409eff;
    border-color: #409eff;
}

.browser-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* 任务日志样式 */
.task-log {
    min-height: 200px;
    max-height: 300px;
    overflow-y: auto;
    background: #2d3748;
    border-radius: 6px;
    padding: 16px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 12px;
    line-height: 1.5;
    border: 1px solid #4a5568;
}

.log-entry {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    padding: 4px 0;
}

.log-time {
    color: #a0aec0;
    font-size: 11px;
    min-width: 80px;
}

.log-entry i {
    min-width: 16px;
}

.log-info i {
    color: #63b3ed;
}

.log-success i {
    color: #68d391;
}

.log-warning i {
    color: #f6e05e;
}

.log-error i {
    color: #fc8181;
}

.log-message {
    color: #e2e8f0;
    flex: 1;
}

/* 表单控件样式调整 */
.el-textarea__inner {
    background: #2d3748;
    color: #e2e8f0;
    border: 1px solid #4a5568;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 12px;
    line-height: 1.5;
}

.el-textarea__inner:focus {
    border-color: #409eff;
    background: #2d3748;
}

.el-textarea__inner::placeholder {
    color: #718096;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .batch-task-content .el-col-8 {
        width: 100% !important;
        flex: 0 0 100% !important;
        margin-bottom: 20px;
    }
    
    .batch-task-content .el-col-16 {
        width: 100% !important;
        flex: 0 0 100% !important;
    }
    
    .batch-task-content .el-row {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .batch-task-page {
        padding: 10px;
    }
    
    .batch-task-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .task-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* 浏览器下载样式 */
.download-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    border-left: 4px solid #007bff;
}

.download-info p {
    margin: 5px 0;
    font-size: 14px;
}

.download-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 8px 0;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 500;
}

.progress-details {
    display: flex;
    justify-content: center;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

#browserDownloadSection {
    background: #fafafa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

#downloadProgress {
    background: #fff;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #ddd;
}
