# 代理认证功能使用指南

## 功能概述

指纹浏览器管理器现已支持带用户名和密码认证的代理IP，解决了之前只能使用无账号密码代理的限制。

## 新增功能

### 1. 代理认证字段
- **代理用户名**: 输入代理服务器的认证用户名
- **代理密码**: 输入代理服务器的认证密码
- 这两个字段都是可选的，如果代理不需要认证可以留空

### 2. 支持的代理格式

#### 无认证代理 (原有功能)
```
http://127.0.0.1:8080
socks5://127.0.0.1:1080
```

#### 带认证代理 (新功能)
- 代理服务器: `http://proxy.example.com:8080`
- 代理用户名: `your_username`
- 代理密码: `your_password`

系统会自动将认证信息合并为: `http://your_username:<EMAIL>:8080`

### 3. 界面更新

#### 配置表单
- 在"代理设置"部分新增了用户名和密码输入框
- 添加了详细的配置说明和示例

#### 配置列表
- 无代理: 显示"直连"
- 无认证代理: 显示"🔒 代理" 
- 认证代理: 显示"🔐 认证代理"

#### 配置预览
- 预览时会显示代理认证状态
- 密码会以 `****` 的形式隐藏显示

## 使用步骤

1. **打开配置界面**
   - 点击"新建配置"或编辑现有配置

2. **填写代理信息**
   - 在"代理设置"部分填写代理服务器地址
   - 如果需要认证，填写用户名和密码
   - 如果不需要认证，用户名和密码留空即可

3. **保存并启动**
   - 保存配置
   - 启动浏览器，系统会自动使用带认证的代理

## 安全说明

- 代理认证信息会安全地传递给Chromium浏览器
- 密码在配置文件中以明文存储，请确保配置文件的安全性
- 在日志输出中，密码会被自动隐藏显示

## 兼容性

- 完全向后兼容现有配置
- 现有的无认证代理配置将继续正常工作
- 新增字段为可选，不影响现有功能

## 示例配置

### HTTP代理with认证
- 代理服务器: `http://proxy.company.com:3128`
- 用户名: `employee123`
- 密码: `securepassword`

### SOCKS5代理with认证
- 代理服务器: `socks5://socks.provider.com:1080`
- 用户名: `user001`
- 密码: `mypassword123`

## 故障排除

### 代理连接问题

1. **ECONNRESET / Socket hang up 错误**
   - 这通常是代理服务器连接中断
   - **解决方案：**
     - 检查代理服务器是否可用
     - 验证用户名和密码是否正确
     - 联系代理服务商确认账户状态
     - 尝试切换到其他代理服务器

2. **ENOTFOUND 错误**
   - 域名解析失败
   - **解决方案：**
     - 检查代理服务器地址是否正确
     - 确认网络连接正常
     - 尝试使用IP地址替代域名

3. **连接超时**
   - 代理服务器响应缓慢或不可达
   - **解决方案：**
     - 检查网络连接速度
     - 尝试更换代理服务器节点
     - 联系代理服务商确认服务状态

### SOCKS代理问题

1. **SOCKS代理认证限制**
   - Chromium对SOCKS代理的认证支持有限
   - **推荐解决方案：**
     - 联系代理提供商设置IP白名单（最佳方案）
     - 使用HTTP代理替代SOCKS代理
     - 在代理服务器配置无认证访问

2. **SOCKS代理无法连接**
   - 检查代理服务器地址和端口
   - 确认防火墙设置允许连接

### 通用问题

3. **特殊字符问题**
   - 用户名和密码中的特殊字符会被自动编码
   - 如遇问题，请尝试使用简单的字符组合

## 更新日志

- ✅ 新增代理用户名和密码字段
- ✅ 更新启动参数生成逻辑支持认证代理
- ✅ 改进配置显示以区分认证状态
- ✅ 保持向后兼容性
- ✅ 安全的密码处理和显示

---

如有问题，请联系: <EMAIL> 